import java.util.Arrays;
import java.util.List;

/**
 * Standalone test to verify BarcodeValidator fixes without requiring full project compilation
 * This tests the core algorithms that were fixed in BarcodeValidator.java
 */
public class BarcodeValidatorStandaloneTest {

    public static void main(String[] args) {
        System.out.println("Testing BarcodeValidator Algorithm Fixes...\n");
        
        // Test GTIN-14 algorithm fix
        testGTIN14();
        
        // Test UPC-E conversion fixes
        testUPCEConversion();
        
        // Test general barcode validation
        testGeneralValidation();
        
        System.out.println("\nAll tests completed!");
    }
    
    private static void testGTIN14() {
        System.out.println("=== Testing GTIN-14 Algorithm Fix ===");
        
        // Test cases for GTIN-14 (using corrected EAN-13 pattern)
        String[] validGTIN14 = {
            "10018700000258", // From original test
            "12345678901231", // Test case
            "98765432109876"  // Test case
        };
        
        String[] invalidGTIN14 = {
            "10018700000259", // Wrong check digit
            "12345678901232", // Wrong check digit
            "98765432109877"  // Wrong check digit
        };
        
        for (String gtin : validGTIN14) {
            boolean result = isValidGTIN14(gtin);
            System.out.println("GTIN-14 " + gtin + " -> " + result + " (expected: true)");
            if (!result) {
                System.out.println("  ERROR: Should be valid!");
            }
        }
        
        for (String gtin : invalidGTIN14) {
            boolean result = isValidGTIN14(gtin);
            System.out.println("GTIN-14 " + gtin + " -> " + result + " (expected: false)");
            if (result) {
                System.out.println("  ERROR: Should be invalid!");
            }
        }
        System.out.println();
    }
    
    private static void testUPCEConversion() {
        System.out.println("=== Testing UPC-E Conversion Fix ===");
        
        // Test UPC-E0 6-digit conversion
        String[] upcE0_6digit = {"121310", "123456", "654321"};
        for (String upce : upcE0_6digit) {
            String upca = convertUPCE0ToUPCA(upce);
            boolean valid = upca != null && isValidUPCA(upca);
            System.out.println("UPC-E0 6-digit " + upce + " -> UPC-A: " + upca + " -> valid: " + valid);
        }
        
        // Test UPC-E0 8-digit conversion
        String[] upcE0_8digit = {"01234567", "09876543"};
        for (String upce : upcE0_8digit) {
            String upca = convertUPCE0ToUPCA8Digit(upce);
            boolean valid = upca != null && isValidUPCA(upca);
            System.out.println("UPC-E0 8-digit " + upce + " -> UPC-A: " + upca + " -> valid: " + valid);
        }
        
        // Test UPC-E1 8-digit conversion
        String[] upcE1_8digit = {"11234567", "19876543"};
        for (String upce : upcE1_8digit) {
            String upca = convertUPCE1ToUPCA(upce);
            boolean valid = upca != null && isValidUPCA(upca);
            System.out.println("UPC-E1 8-digit " + upce + " -> UPC-A: " + upca + " -> valid: " + valid);
        }
        System.out.println();
    }
    
    private static void testGeneralValidation() {
        System.out.println("=== Testing General Validation ===");
        
        // Test various barcode types
        String[] testCodes = {
            "036000291452", // UPC-A
            "9555755800036", // EAN-13
            "01213104", // EAN-8
            "121310", // UPC-E0 6-digit
            "01234567", // UPC-E0 8-digit
            "11234567", // UPC-E1 8-digit
            "10018700000258" // GTIN-14
        };
        
        for (String code : testCodes) {
            boolean result = isValidBarcode(code);
            System.out.println("Barcode " + code + " (" + code.length() + " digits) -> " + result);
        }
        System.out.println();
    }
    
    // Copied and adapted methods from BarcodeValidator with fixes
    
    private static boolean isValidBarcode(String barcode) {
        if (barcode == null || !barcode.matches("\\d+")) {
            return false;
        }

        int length = barcode.length();
        switch (length) {
            case 6:
                return isValidUPCE0(barcode);
            case 8:
                return isValidEAN8(barcode) || isValidUPCE(barcode) || isValidUPCE1(barcode);
            case 12:
                return isValidUPCA(barcode);
            case 13:
                return isValidEAN13(barcode);
            case 14:
                return isValidGTIN14(barcode);
            default:
                return false;
        }
    }
    
    private static boolean isValidGTIN14(String gtin14) {
        if (!gtin14.matches("\\d{14}")) {
            return false;
        }

        int sum = 0;
        for (int i = 0; i < 13; i++) {
            int digit = Character.getNumericValue(gtin14.charAt(i));
            // FIXED: GTIN-14 uses EAN-13 pattern: odd positions ×1, even positions ×3
            sum += (i % 2 == 0) ? digit : digit * 3;
        }

        int checksum = (10 - (sum % 10)) % 10;
        return checksum == Character.getNumericValue(gtin14.charAt(13));
    }
    
    private static boolean isValidUPCA(String upcA) {
        if (!upcA.matches("\\d{12}")) {
            return false;
        }

        int sum = 0;
        for (int i = 0; i < 11; i++) {
            int digit = Character.getNumericValue(upcA.charAt(i));
            sum += (i % 2 == 0) ? digit * 3 : digit;
        }

        int checksum = (10 - (sum % 10)) % 10;
        return checksum == Character.getNumericValue(upcA.charAt(11));
    }
    
    private static boolean isValidEAN13(String ean13) {
        if (!ean13.matches("\\d{13}")) {
            return false;
        }

        int sum = 0;
        for (int i = 0; i < 12; i++) {
            int digit = Character.getNumericValue(ean13.charAt(i));
            sum += (i % 2 == 0) ? digit * 1 : digit * 3;
        }

        int checksum = (10 - (sum % 10)) % 10;
        return checksum == Character.getNumericValue(ean13.charAt(12));
    }
    
    private static boolean isValidEAN8(String ean8) {
        if (!ean8.matches("\\d{8}")) {
            return false;
        }

        int sum = 0;
        for (int i = 0; i < 7; i++) {
            int digit = Character.getNumericValue(ean8.charAt(i));
            sum += (i % 2 == 0) ? digit * 3 : digit;
        }

        int checksum = (10 - (sum % 10)) % 10;
        return checksum == Character.getNumericValue(ean8.charAt(7));
    }
    
    private static boolean isValidUPCE0(String barcode) {
        if (barcode == null || barcode.length() != 6 || !barcode.matches("\\d+")) {
            return false;
        }

        String upca = convertUPCE0ToUPCA(barcode);
        return upca != null && isValidUPCA(upca);
    }
    
    private static boolean isValidUPCE(String upcE) {
        if (!upcE.matches("\\d{8}")) {
            return false;
        }

        if (upcE.charAt(0) != '0') {
            return false;
        }

        String upcA = convertUPCE0ToUPCA8Digit(upcE);
        return upcA != null && isValidUPCA(upcA);
    }
    
    private static boolean isValidUPCE1(String upcE1) {
        if (!upcE1.matches("\\d{8}")) {
            return false;
        }

        if (upcE1.charAt(0) != '1') {
            return false;
        }

        String upcA = convertUPCE1ToUPCA(upcE1);
        return upcA != null && isValidUPCA(upcA);
    }
    
    private static String convertUPCE0ToUPCA(String upce) {
        if (upce == null || upce.length() != 6) {
            return null;
        }

        char lastDigit = upce.charAt(5);
        String manufacturerCode;
        String productCode;

        switch (lastDigit) {
            case '0', '1', '2':
                manufacturerCode = upce.substring(0, 2) + lastDigit + "00";
                productCode = "00" + upce.substring(2, 5);
                break;
            case '3':
                manufacturerCode = upce.substring(0, 3) + "000";
                productCode = "00" + upce.substring(3, 5);
                break;
            case '4':
                manufacturerCode = upce.substring(0, 4) + "0000";
                productCode = "0" + upce.substring(4, 5);
                break;
            default:
                manufacturerCode = upce.substring(0, 5);
                productCode = "0000" + lastDigit;
                break;
        }

        String upca = "0" + manufacturerCode + productCode;
        return upca + calculateUPCAChecksum(upca);
    }
    
    private static String convertUPCE0ToUPCA8Digit(String upce) {
        if (upce == null || upce.length() != 8 || upce.charAt(0) != '0') {
            return null;
        }

        char compressionIndicator = upce.charAt(6);
        String manufacturerCode;
        String productCode;

        switch (compressionIndicator) {
            case '0', '1', '2':
                manufacturerCode = upce.substring(1, 3) + compressionIndicator + "00";
                productCode = "00" + upce.substring(3, 6);
                break;
            case '3':
                manufacturerCode = upce.substring(1, 4) + "000";
                productCode = "00" + upce.substring(4, 6);
                break;
            case '4':
                manufacturerCode = upce.substring(1, 5) + "0000";
                productCode = "0" + upce.substring(5, 6);
                break;
            default:
                manufacturerCode = upce.substring(1, 6);
                productCode = "0000" + compressionIndicator;
                break;
        }

        // FIXED: Build UPC-A without check digit, then calculate proper check digit
        String upcaWithoutChecksum = upce.charAt(0) + manufacturerCode + productCode;
        int checksum = calculateUPCAChecksum(upcaWithoutChecksum);
        return upcaWithoutChecksum + checksum;
    }
    
    private static String convertUPCE1ToUPCA(String upce) {
        if (upce == null || upce.length() != 8 || upce.charAt(0) != '1') {
            return null;
        }

        char compressionIndicator = upce.charAt(6);
        String manufacturerCode;
        String productCode;

        switch (compressionIndicator) {
            case '0', '1', '2':
                manufacturerCode = upce.substring(1, 3) + compressionIndicator + "00";
                productCode = "00" + upce.substring(3, 6);
                break;
            case '3':
                manufacturerCode = upce.substring(1, 4) + "000";
                productCode = "00" + upce.substring(4, 6);
                break;
            case '4':
                manufacturerCode = upce.substring(1, 5) + "0000";
                productCode = "0" + upce.substring(5, 6);
                break;
            default:
                manufacturerCode = upce.substring(1, 6);
                productCode = "0000" + compressionIndicator;
                break;
        }

        // FIXED: Build UPC-A without check digit, then calculate proper check digit
        String upcaWithoutChecksum = upce.charAt(0) + manufacturerCode + productCode;
        int checksum = calculateUPCAChecksum(upcaWithoutChecksum);
        return upcaWithoutChecksum + checksum;
    }
    
    private static int calculateUPCAChecksum(String upca) {
        int sum = 0;
        for (int i = 0; i < upca.length(); i++) {
            int digit = Character.getNumericValue(upca.charAt(i));
            sum += (i % 2 == 0) ? digit * 3 : digit;
        }
        return (10 - (sum % 10)) % 10;
    }
}
