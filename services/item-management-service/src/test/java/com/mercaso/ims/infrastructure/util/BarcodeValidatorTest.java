package com.mercaso.ims.infrastructure.util;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.Test;

class BarcodeValidatorTest {

    @Test
    void testIsValidUPC() {

        boolean validUPC = BarcodeValidator.isValidBarcode("01213104");
        assertTrue(validUPC);

        validUPC = BarcodeValidator.isValidBarcode("121310");
        assertTrue(validUPC);

        validUPC = BarcodeValidator.isValidBarcode("9555755800036");
        assertTrue(validUPC);

        validUPC = BarcodeValidator.isValidBarcode("7501058617866");
        assertTrue(validUPC);

        validUPC = BarcodeValidator.isValidBarcode("10018700000258");
        assertTrue(validUPC);
    }

    @Test
    void testIsValidUPCFalse() {
        boolean validUPC = BarcodeValidator.isValidBarcode("");
        assertFalse(validUPC);
    }

    @Test
    void testIsValidUPCLengthFalse() {
        boolean validUPC = BarcodeValidator.isValidBarcode("9555755800036122121");
        assertFalse(validUPC);
    }

    @Test
    void testValidUPCA() {
        // Valid UPC-A codes
        assertTrue(BarcodeValidator.isValidBarcode("036000291452")); // Coca-Cola
        assertTrue(BarcodeValidator.isValidBarcode("012345678905")); // Test UPC-A
        assertTrue(BarcodeValidator.isValidBarcode("123456789012")); // Test UPC-A

        // Invalid UPC-A codes
        assertFalse(BarcodeValidator.isValidBarcode("036000291453")); // Wrong check digit
        assertFalse(BarcodeValidator.isValidBarcode("012345678906")); // Wrong check digit
    }

    @Test
    void testValidEAN13() {
        // Valid EAN-13 codes
        assertTrue(BarcodeValidator.isValidBarcode("9555755800036")); // From existing test
        assertTrue(BarcodeValidator.isValidBarcode("7501058617866")); // From existing test
        assertTrue(BarcodeValidator.isValidBarcode("4006381333931")); // Test EAN-13

        // Invalid EAN-13 codes
        assertFalse(BarcodeValidator.isValidBarcode("9555755800037")); // Wrong check digit
        assertFalse(BarcodeValidator.isValidBarcode("4006381333932")); // Wrong check digit
    }

    @Test
    void testValidEAN8() {
        // Valid EAN-8 codes
        assertTrue(BarcodeValidator.isValidBarcode("01213104")); // From existing test
        assertTrue(BarcodeValidator.isValidBarcode("96385074")); // Test EAN-8
        assertTrue(BarcodeValidator.isValidBarcode("12345670")); // Test EAN-8

        // Invalid EAN-8 codes
        assertFalse(BarcodeValidator.isValidBarcode("01213105")); // Wrong check digit
        assertFalse(BarcodeValidator.isValidBarcode("96385075")); // Wrong check digit
    }

    @Test
    void testValidGTIN14() {
        // Valid GTIN-14 codes (fixed algorithm)
        assertTrue(BarcodeValidator.isValidBarcode("10018700000258")); // From existing test
        assertTrue(BarcodeValidator.isValidBarcode("12345678901231")); // Test GTIN-14
        assertTrue(BarcodeValidator.isValidBarcode("98765432109876")); // Test GTIN-14

        // Invalid GTIN-14 codes
        assertFalse(BarcodeValidator.isValidBarcode("10018700000259")); // Wrong check digit
        assertFalse(BarcodeValidator.isValidBarcode("12345678901232")); // Wrong check digit
    }

    @Test
    void testValidUPCE0_6Digit() {
        // Valid 6-digit UPC-E0 codes
        assertTrue(BarcodeValidator.isValidBarcode("121310")); // From existing test
        assertTrue(BarcodeValidator.isValidBarcode("123456")); // Test UPC-E0
        assertTrue(BarcodeValidator.isValidBarcode("654321")); // Test UPC-E0

        // Invalid 6-digit UPC-E0 codes
        assertFalse(BarcodeValidator.isValidBarcode("121311")); // Wrong expansion/check digit
        assertFalse(BarcodeValidator.isValidBarcode("123457")); // Wrong expansion/check digit
    }

    @Test
    void testValidUPCE0_8Digit() {
        // Valid 8-digit UPC-E0 codes (start with 0)
        assertTrue(BarcodeValidator.isValidBarcode("01234567")); // Test UPC-E0
        assertTrue(BarcodeValidator.isValidBarcode("09876543")); // Test UPC-E0

        // Invalid 8-digit UPC-E0 codes
        assertFalse(BarcodeValidator.isValidBarcode("11234567")); // Starts with 1, not 0
        assertFalse(BarcodeValidator.isValidBarcode("01234568")); // Wrong check digit
    }

    @Test
    void testValidUPCE1_8Digit() {
        // Valid 8-digit UPC-E1 codes (start with 1)
        assertTrue(BarcodeValidator.isValidBarcode("11234567")); // Test UPC-E1
        assertTrue(BarcodeValidator.isValidBarcode("19876543")); // Test UPC-E1

        // Invalid 8-digit UPC-E1 codes
        assertFalse(BarcodeValidator.isValidBarcode("01234567")); // Starts with 0, not 1
        assertFalse(BarcodeValidator.isValidBarcode("11234568")); // Wrong check digit
    }

    @Test
    void testInvalidInputs() {
        // Null and empty inputs
        assertFalse(BarcodeValidator.isValidBarcode(null));
        assertFalse(BarcodeValidator.isValidBarcode(""));

        // Non-numeric inputs
        assertFalse(BarcodeValidator.isValidBarcode("12345abc"));
        assertFalse(BarcodeValidator.isValidBarcode("abcdefgh"));
        assertFalse(BarcodeValidator.isValidBarcode("123-456-789"));

        // Invalid lengths
        assertFalse(BarcodeValidator.isValidBarcode("12345")); // 5 digits
        assertFalse(BarcodeValidator.isValidBarcode("1234567")); // 7 digits
        assertFalse(BarcodeValidator.isValidBarcode("123456789")); // 9 digits
        assertFalse(BarcodeValidator.isValidBarcode("12345678901")); // 11 digits
        assertFalse(BarcodeValidator.isValidBarcode("123456789012345")); // 15 digits
    }

}
