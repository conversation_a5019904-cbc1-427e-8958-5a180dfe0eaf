package com.mercaso.ims.infrastructure.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@Slf4j
public class BarcodeValidator {

    private BarcodeValidator() {
    }

    public static boolean isValidBarcode(String barcode) {
        if (barcode == null || !barcode.matches("\\d+")) {
            return false;
        }

        int length = barcode.length();
        switch (length) {
            case 6:
                return isValidUPCE0(barcode);
            case 8:
                return isValidEAN8(barcode) || isValidUPCE(barcode) || isValidUPCE1(barcode);
            case 12:
                return isValidUPCA(barcode);
            case 13:
                return isValidEAN13(barcode);
            case 14:
                return isValidGTIN14(barcode);
            default:
                return false;
        }
    }

    public static boolean isValidUPCE0(String barcode) {
        // Validate length
        if (barcode == null || barcode.length() != 6 || !barcode.matches("\\d+")) {
            return false;
        }

        // Convert to UPC-A format
        String upca = convertUPCE0ToUPCA(barcode);
        return upca != null && isValidUPCA(upca);
    }

    private static String convertUPCE0ToUPCA(String upce) {
        if (upce == null || upce.length() != 6) {
            return null;
        }

        char lastDigit = upce.charAt(5); // Compression indicator
        String manufacturerCode;
        String productCode;

        switch (lastDigit) {
            case '0', '1', '2':
                manufacturerCode = upce.substring(0, 2) + lastDigit + "00";
                productCode = "00" + upce.substring(2, 5);
                break;
            case '3':
                manufacturerCode = upce.substring(0, 3) + "000";
                productCode = "00" + upce.substring(3, 5);
                break;
            case '4':
                manufacturerCode = upce.substring(0, 4) + "0000";
                productCode = "0" + upce.substring(4, 5);
                break;
            default:
                manufacturerCode = upce.substring(0, 5);
                productCode = "0000" + lastDigit;
                break;
        }

        // Combine system number, manufacturer code, product code, and checksum placeholder
        String upca = "0" + manufacturerCode + productCode;
        return upca + calculateUPCAChecksum(upca);
    }

    private static int calculateUPCAChecksum(String upca) {
        int sum = 0;
        for (int i = 0; i < upca.length(); i++) {
            int digit = Character.getNumericValue(upca.charAt(i));
            sum += (i % 2 == 0) ? digit * 3 : digit;
        }
        return (10 - (sum % 10)) % 10;
    }

    private static boolean isValidUPCA(String upcA) {
        if (!upcA.matches("\\d{12}")) {
            return false;
        }

        int sum = 0;
        for (int i = 0; i < 11; i++) {
            int digit = Character.getNumericValue(upcA.charAt(i));
            sum += (i % 2 == 0) ? digit * 3 : digit;
        }

        int checksum = (10 - (sum % 10)) % 10;
        return checksum == Character.getNumericValue(upcA.charAt(11));
    }

    private static boolean isValidEAN13(String ean13) {
        if (!ean13.matches("\\d{13}")) {
            return false;
        }

        int sum = 0;
        for (int i = 0; i < 12; i++) {
            int digit = Character.getNumericValue(ean13.charAt(i));
            sum += (i % 2 == 0) ? digit * 1 : digit * 3;
        }

        int checksum = (10 - (sum % 10)) % 10;
        return checksum == Character.getNumericValue(ean13.charAt(12));
    }

    private static boolean isValidEAN8(String ean8) {
        if (!ean8.matches("\\d{8}")) {
            return false;
        }

        int sum = 0;
        for (int i = 0; i < 7; i++) {
            int digit = Character.getNumericValue(ean8.charAt(i));
            sum += (i % 2 == 0) ? digit * 3 : digit;
        }

        int checksum = (10 - (sum % 10)) % 10;
        return checksum == Character.getNumericValue(ean8.charAt(7));
    }

    private static boolean isValidUPCE(String upcE) {
        if (!upcE.matches("\\d{8}")) {
            return false;
        }

        // UPC-E0 must start with '0'
        if (upcE.charAt(0) != '0') {
            return false;
        }

        try {
            String upcA = convertUPCE0ToUPCA8Digit(upcE);
            if (!StringUtils.isBlank(upcA)) {
                return isValidUPCA(upcA);
            } else {
                return false;
            }
        } catch (IllegalArgumentException e) {
            return false;
        }
    }

    private static boolean isValidUPCE1(String upcE1) {
        if (!upcE1.matches("\\d{8}")) {
            return false;
        }

        // UPC-E1 must start with '1'
        if (upcE1.charAt(0) != '1') {
            return false;
        }

        try {
            String upcA = convertUPCE1ToUPCA(upcE1);
            if (!StringUtils.isBlank(upcA)) {
                return isValidUPCA(upcA);
            } else {
                return false;
            }
        } catch (IllegalArgumentException e) {
            return false;
        }
    }

    private static String convertUPCE0ToUPCA8Digit(String upce) {
        if (upce == null || upce.length() != 8) {
            return null;
        }

        char firstDigit = upce.charAt(0);
        if (firstDigit != '0') {
            return null;
        }

        char compressionIndicator = upce.charAt(6);
        String manufacturerCode;
        String productCode;

        switch (compressionIndicator) {
            case '0', '1', '2':
                manufacturerCode = upce.substring(1, 3) + compressionIndicator + "00";
                productCode = "00" + upce.substring(3, 6);
                break;
            case '3':
                manufacturerCode = upce.substring(1, 4) + "000";
                productCode = "00" + upce.substring(4, 6);
                break;
            case '4':
                manufacturerCode = upce.substring(1, 5) + "0000";
                productCode = "0" + upce.substring(5, 6);
                break;
            default:
                manufacturerCode = upce.substring(1, 6);
                productCode = "0000" + compressionIndicator;
                break;
        }

        // Build UPC-A without check digit, then calculate proper check digit
        String upcaWithoutChecksum = firstDigit + manufacturerCode + productCode;
        int checksum = calculateUPCAChecksum(upcaWithoutChecksum);
        return upcaWithoutChecksum + checksum;
    }

    private static String convertUPCE1ToUPCA(String upce) {
        if (upce == null || upce.length() != 8) {
            return null;
        }

        char firstDigit = upce.charAt(0);
        if (firstDigit != '1') {
            return null;
        }

        char compressionIndicator = upce.charAt(6);
        String manufacturerCode;
        String productCode;

        switch (compressionIndicator) {
            case '0', '1', '2':
                manufacturerCode = upce.substring(1, 3) + compressionIndicator + "00";
                productCode = "00" + upce.substring(3, 6);
                break;
            case '3':
                manufacturerCode = upce.substring(1, 4) + "000";
                productCode = "00" + upce.substring(4, 6);
                break;
            case '4':
                manufacturerCode = upce.substring(1, 5) + "0000";
                productCode = "0" + upce.substring(5, 6);
                break;
            default:
                manufacturerCode = upce.substring(1, 6);
                productCode = "0000" + compressionIndicator;
                break;
        }

        // Build UPC-A without check digit, then calculate proper check digit
        String upcaWithoutChecksum = firstDigit + manufacturerCode + productCode;
        int checksum = calculateUPCAChecksum(upcaWithoutChecksum);
        return upcaWithoutChecksum + checksum;
    }

    private static boolean isValidGTIN14(String gtin14) {
        if (!gtin14.matches("\\d{14}")) {
            return false;
        }

        int sum = 0;
        for (int i = 0; i < 13; i++) {
            int digit = Character.getNumericValue(gtin14.charAt(i));
            // GTIN-14 uses EAN-13 pattern: odd positions ×1, even positions ×3
            sum += (i % 2 == 0) ? digit : digit * 3;
        }

        int checksum = (10 - (sum % 10)) % 10;
        return checksum == Character.getNumericValue(gtin14.charAt(13));
    }


}
